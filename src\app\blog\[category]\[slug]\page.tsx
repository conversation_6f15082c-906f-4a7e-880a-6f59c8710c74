import { type Metadata } from "next";
import {
  allBlogPosts,
  allBlogCategories,
  allBlogAuthors,
} from "~/types/contentlayer";
import { SITE_CONFIG, SITE_DOMAIN } from "~/constants/siteConfig";
import { generateArticleSchema } from "~/lib/structured-data";
import Script from "next/script";
import { MDXContent } from "~/components/blog/mdx-content";
import BlogPostHeader from "~/components/blog/blog-post-header";
import BlogPostFooter from "~/components/blog/blog-post-footer";
import BlogSidebar from "~/components/blog/blog-sidebar";
import RelatedPosts from "~/components/blog/related-posts";
import { getBlogMetadata, getRelatedPosts } from "~/lib/blog-utils";

// ============================================================================
// Static Params Generation
// ============================================================================

export async function generateStaticParams() {
  return allBlogPosts.map((post) => ({
    category: post.categorySlug,
    slug: post.slug,
  }));
}

// ============================================================================
// Metadata Generation
// ============================================================================

export async function generateMetadata({
  params,
}: {
  params: { category: string; slug: string };
}): Promise<Metadata> {
  const post = allBlogPosts.find(
    (post) =>
      post.categorySlug === params.category && post.slug === params.slug,
  );

  if (!post || post.draft) {
    return {
      title: "Post Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  const category = allBlogCategories.find(
    (cat) => cat.slug === params.category,
  );
  const author = allBlogAuthors.find((auth) => auth.slug === post.author);

  // Use custom SEO title or generate from post title
  const seoTitle =
    post.seo?.title || `${post.title} | NEET Blog - Aims Academy`;

  // Use custom SEO description or post description (truncated to 150 chars)
  const seoDescription =
    post.seo?.description ||
    (post.description.length > 147
      ? post.description.substring(0, 147) + "..."
      : post.description);

  return {
    title: seoTitle,
    description: seoDescription,
    keywords: [
      ...post.tags,
      ...(post.seo?.keywords || []),
      "NEET Preparation",
      "Medical Entrance",
      "Aims Academy",
      category?.name || "",
    ].filter(Boolean),
    alternates: {
      canonical: post.seo?.canonicalUrl || `${SITE_DOMAIN}${post.url}`,
    },
    robots: {
      index: !post.seo?.noindex,
      follow: !post.seo?.noindex,
    },
    openGraph: {
      title: post.title,
      description: seoDescription,
      url: `${SITE_DOMAIN}${post.url}`,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: post.image?.src || "/blog/default-post-image.jpg",
          width: 1200,
          height: 630,
          alt: post.image?.alt || post.title,
        },
      ],
      locale: "en_IN",
      type: "article",
      publishedTime: post.publishedAt,
      modifiedTime: post.updatedAt || post.publishedAt,
      authors: [author?.name || post.author],
      tags: post.tags,
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: seoDescription,
      images: [post.image?.src || "/blog/default-post-image.jpg"],
      creator: author?.social?.twitter,
    },
  };
}

// ============================================================================
// Blog Post Page Component
// ============================================================================

export default async function BlogPostPage({
  params,
}: {
  params: { category: string; slug: string };
}) {
  // Find the blog post
  const post = allBlogPosts.find(
    (post) =>
      post.categorySlug === params.category && post.slug === params.slug,
  );

  if (!post || post.draft) {
    return <div>Post not found</div>;
  }

  // TypeScript doesn't know that notFound() throws, so we assert post is not null
  const safePost = post!;

  // Get related data
  const category = allBlogCategories.find(
    (cat) => cat.slug === params.category,
  );
  const author = allBlogAuthors.find((auth) => auth.slug === safePost.author);
  const allCategories = allBlogCategories;

  // Get related posts
  const relatedPosts = getRelatedPosts(safePost, allBlogPosts, 3);

  // Get recent posts for sidebar
  const recentPosts = allBlogPosts
    .filter((p) => !p.draft && p.slug !== safePost.slug)
    .sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime(),
    )
    .slice(0, 5);

  // Get blog metadata
  const blogMetadata = getBlogMetadata(allBlogPosts.filter((p) => !p.draft));

  // Generate structured data
  const articleSchema = generateArticleSchema({
    title: safePost.title,
    description: safePost.description,
    url: `${SITE_DOMAIN}${safePost.url}`,
    datePublished: safePost.publishedAt,
    dateModified: safePost.updatedAt || safePost.publishedAt,
    authorName: author?.name || safePost.author,
    authorBio: author?.bio,
    category: category?.name || params.category,
    tags: safePost.tags,
    image: safePost.image?.src,
    wordCount: safePost.wordCount,
    readingTime: safePost.readingTime,
  });

  return (
    <>
      <article className="min-h-screen bg-background">
        {/* Blog Post Header */}
        <BlogPostHeader post={safePost} category={category} author={author} />

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Post Content */}
              <div className="prose prose-lg max-w-none">
                <MDXContent code={safePost.body.code} />
              </div>

              {/* Post Footer */}
              <BlogPostFooter
                post={safePost}
                author={author}
                category={category}
              />

              {/* Related Posts */}
              {relatedPosts.length > 0 && (
                <div className="mt-16">
                  <RelatedPosts posts={relatedPosts} />
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <BlogSidebar
                categories={allCategories}
                recentPosts={recentPosts}
                popularPosts={blogMetadata.popularPosts}
                currentCategory={params.category}
              />
            </div>
          </div>
        </div>
      </article>

      {/* Structured Data */}
      <Script
        id="article-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(articleSchema),
        }}
      />
    </>
  );
}
