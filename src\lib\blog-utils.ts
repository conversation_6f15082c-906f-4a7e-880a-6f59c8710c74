import {
  type BlogPost,
  type BlogMetadata,
  type BlogSearchParams,
  type BlogSearchResult,
} from "~/types/blog";
import { type BlogPost as ContentlayerBlogPost } from "~/types/contentlayer";

// ============================================================================
// Blog Data Processing Utilities
// ============================================================================

/**
 * Get blog metadata including featured, popular, and recent posts
 */
export function getBlogMetadata(posts: ContentlayerBlogPost[]): BlogMetadata {
  const publishedPosts = posts.filter((post) => !post.draft);

  // Sort posts by date
  const sortedPosts = publishedPosts.sort(
    (a, b) =>
      new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime(),
  );

  // Get featured posts
  const featuredPosts = publishedPosts
    .filter((post) => post.featured)
    .slice(0, 3);

  // Get popular posts (for now, use featured posts or recent posts)
  const popularPosts =
    featuredPosts.length > 0 ? featuredPosts : sortedPosts.slice(0, 3);

  // Get recent posts
  const recentPosts = sortedPosts.slice(0, 5);

  // Get unique categories
  const categories = [
    ...new Set(publishedPosts.map((post) => post.categorySlug)),
  ];

  // Get unique tags
  const allTags = publishedPosts.flatMap((post) => post.tags || []);
  const uniqueTags = [...new Set(allTags)];

  return {
    totalPosts: publishedPosts.length,
    totalCategories: categories.length,
    totalTags: uniqueTags.length,
    lastUpdated: sortedPosts[0]?.publishedAt || new Date().toISOString(),
    featuredPosts: featuredPosts as unknown as BlogPost[],
    popularPosts: popularPosts as unknown as BlogPost[],
    recentPosts: recentPosts as unknown as BlogPost[],
  };
}

/**
 * Get related posts based on category and tags
 */
export function getRelatedPosts(
  currentPost: ContentlayerBlogPost,
  allPosts: ContentlayerBlogPost[],
  limit: number = 3,
): ContentlayerBlogPost[] {
  const publishedPosts = allPosts.filter(
    (post) => !post.draft && post.slug !== currentPost.slug,
  );

  // Score posts based on similarity
  const scoredPosts = publishedPosts.map((post) => {
    let score = 0;

    // Same category gets high score
    if (post.categorySlug === currentPost.categorySlug) {
      score += 10;
    }

    // Shared tags get points
    const sharedTags = (post.tags || []).filter((tag) =>
      (currentPost.tags || []).includes(tag),
    );
    score += sharedTags.length * 3;

    // Same author gets points
    if (post.author === currentPost.author) {
      score += 2;
    }

    return { post, score };
  });

  // Sort by score and return top posts
  return scoredPosts
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map((item) => item.post);
}

/**
 * Search and filter blog posts
 */
export function searchBlogPosts(
  posts: ContentlayerBlogPost[],
  params: BlogSearchParams,
): BlogSearchResult {
  let filteredPosts = posts.filter((post) => !post.draft);

  // Filter by search query
  if (params.query) {
    const query = params.query.toLowerCase();
    filteredPosts = filteredPosts.filter(
      (post) =>
        post.title.toLowerCase().includes(query) ||
        post.description.toLowerCase().includes(query) ||
        post.excerpt.toLowerCase().includes(query) ||
        (post.tags || []).some((tag) => tag.toLowerCase().includes(query)),
    );
  }

  // Filter by category
  if (params.category) {
    filteredPosts = filteredPosts.filter(
      (post) => post.categorySlug === params.category,
    );
  }

  // Filter by tag
  if (params.tag) {
    filteredPosts = filteredPosts.filter((post) =>
      (post.tags || []).includes(params.tag!),
    );
  }

  // Filter by author
  if (params.author) {
    filteredPosts = filteredPosts.filter(
      (post) => post.author === params.author,
    );
  }

  // Sort posts
  const sortBy = params.sortBy || "publishedAt";
  const sortOrder = params.sortOrder || "desc";

  filteredPosts.sort((a, b) => {
    let aValue: string | number;
    let bValue: string | number;

    switch (sortBy) {
      case "publishedAt":
        aValue = new Date(a.publishedAt).getTime();
        bValue = new Date(b.publishedAt).getTime();
        break;
      case "updatedAt":
        aValue = new Date(a.updatedAt || a.publishedAt).getTime();
        bValue = new Date(b.updatedAt || b.publishedAt).getTime();
        break;
      case "title":
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
        break;
      case "readingTime":
        aValue = a.readingTime;
        bValue = b.readingTime;
        break;
      default:
        aValue = new Date(a.publishedAt).getTime();
        bValue = new Date(b.publishedAt).getTime();
    }

    if (sortOrder === "asc") {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // Pagination
  const page = params.page || 1;
  const limit = params.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedPosts = filteredPosts.slice(startIndex, endIndex);

  const totalPages = Math.ceil(filteredPosts.length / limit);

  return {
    posts: paginatedPosts as unknown as BlogPost[],
    totalCount: filteredPosts.length,
    currentPage: page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };
}

/**
 * Get tag cloud data with counts
 */
export function getTagCloud(
  posts: ContentlayerBlogPost[],
  maxTags: number = 20,
) {
  const publishedPosts = posts.filter((post) => !post.draft);

  // Count tag occurrences
  const tagCounts = new Map<string, number>();

  publishedPosts.forEach((post) => {
    (post.tags || []).forEach((tag) => {
      tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
    });
  });

  // Convert to array and sort by count
  const tagArray = Array.from(tagCounts.entries())
    .map(([name, count]) => ({
      name,
      count,
      slug: name.toLowerCase().replace(/\s+/g, "-"),
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, maxTags);

  return tagArray;
}

/**
 * Generate excerpt from content
 */
export function generateExcerpt(
  content: string,
  maxLength: number = 160,
): string {
  // Remove markdown formatting
  const plainText = content.replace(/[#*`]/g, "").replace(/\n+/g, " ").trim();

  if (plainText.length <= maxLength) {
    return plainText;
  }

  // Find the last complete word within the limit
  const truncated = plainText.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(" ");

  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + "...";
  }

  return truncated + "...";
}

/**
 * Format reading time
 */
export function formatReadingTime(minutes: number): string {
  if (minutes < 1) {
    return "Less than 1 min read";
  }

  return `${Math.ceil(minutes)} min read`;
}

/**
 * Format publish date
 */
export function formatPublishDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-IN", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}
