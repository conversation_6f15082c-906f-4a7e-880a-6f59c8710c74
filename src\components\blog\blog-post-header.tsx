import Image from "next/image";
import Link from "next/link";
import {
  type BlogPost as ContentlayerBlogPost,
  type Blog<PERSON>ategory as ContentlayerBlogCategory,
  type <PERSON><PERSON><PERSON>uth<PERSON> as ContentlayerBlogAuthor,
} from "~/types/contentlayer";
import { formatPublishDate, formatReadingTime } from "~/lib/blog-utils";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  Clock,
  Calendar,
  User,
  ArrowLeft,
  Share2,
  Bookmark,
  Eye,
} from "lucide-react";

interface BlogPostHeaderProps {
  post: ContentlayerBlogPost;
  category?: ContentlayerBlogCategory;
  author?: ContentlayerBlogAuthor;
}

export default function BlogPostHeader({
  post,
  category,
  author,
}: BlogPostHeaderProps) {
  return (
    <header className="relative">
      {/* Back Navigation */}
      <div className="container mx-auto px-4 py-4">
        <Link
          href="/blog"
          className="inline-flex items-center gap-2 text-muted-foreground transition-colors hover:text-primary"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Blog</span>
        </Link>
      </div>

      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-primary/5 via-background to-accent/5">
        {/* Featured Image */}
        {post.image && (
          <div className="relative h-64 overflow-hidden md:h-80 lg:h-96">
            <Image
              src={post.image.src}
              alt={post.image.alt}
              fill
              className="object-cover"
              sizes="100vw"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

            {/* Content Overlay */}
            <div className="absolute inset-0 flex items-end">
              <div className="container mx-auto px-4 pb-8">
                <div className="max-w-4xl">
                  {/* Category Badge */}
                  {category && (
                    <Link href={`/blog/${category.slug}`}>
                      <Badge
                        variant="secondary"
                        className="mb-4 bg-background/90 text-foreground hover:bg-background"
                      >
                        {category.name}
                      </Badge>
                    </Link>
                  )}

                  {/* Title */}
                  <h1 className="mb-4 text-3xl font-bold leading-tight text-white md:text-4xl lg:text-5xl">
                    {post.title}
                  </h1>

                  {/* Meta Information */}
                  <div className="flex flex-wrap items-center gap-4 text-white/90">
                    {author && (
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={author.avatar} alt={author.name} />
                          <AvatarFallback className="bg-primary text-primary-foreground">
                            {author.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{author.name}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatPublishDate(post.publishedAt)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{formatReadingTime(post.readingTime)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      <span>{post.wordCount} words</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* No Image Layout */}
        {!post.image && (
          <div className="container mx-auto px-4 py-16">
            <div className="mx-auto max-w-4xl space-y-6 text-center">
              {/* Category Badge */}
              {category && (
                <Link href={`/blog/${category.slug}`}>
                  <Badge
                    variant="secondary"
                    className="bg-primary/10 text-primary hover:bg-primary/20"
                  >
                    {category.name}
                  </Badge>
                </Link>
              )}

              {/* Title */}
              <h1 className="text-3xl font-bold leading-tight text-primary md:text-4xl lg:text-5xl">
                {post.title}
              </h1>

              {/* Description */}
              <p className="mx-auto max-w-3xl text-lg text-muted-foreground md:text-xl">
                {post.description}
              </p>

              {/* Meta Information */}
              <div className="flex flex-wrap items-center justify-center gap-6 text-muted-foreground">
                {author && (
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={author.avatar} alt={author.name} />
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        {author.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{author.name}</span>
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{formatPublishDate(post.publishedAt)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{formatReadingTime(post.readingTime)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  <span>{post.wordCount} words</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="absolute right-4 top-4 flex gap-2">
          <Button
            size="sm"
            variant="secondary"
            className="bg-background/90 hover:bg-background"
          >
            <Share2 className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="bg-background/90 hover:bg-background"
          >
            <Bookmark className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Tags Section */}
      {post.tags && post.tags.length > 0 && (
        <div className="border-b bg-background">
          <div className="container mx-auto px-4 py-4">
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-muted-foreground">
                Tags:
              </span>
              {post.tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="cursor-pointer text-xs transition-colors hover:bg-primary hover:text-primary-foreground"
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Progress Bar (for reading progress - would need client-side implementation) */}
      <div className="h-1 bg-muted">
        <div
          className="h-full w-0 bg-primary transition-all duration-300"
          id="reading-progress"
        />
      </div>
    </header>
  );
}
