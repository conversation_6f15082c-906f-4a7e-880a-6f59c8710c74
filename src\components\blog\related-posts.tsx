import Link from "next/link";
import Image from "next/image";
import { type BlogPost as ContentlayerBlogPost } from "~/types/contentlayer";
import { formatPublishDate, formatReadingTime } from "~/lib/blog-utils";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent } from "~/components/ui/card";
import { Clock, Calendar, ArrowRight, BookOpen } from "lucide-react";

interface RelatedPostsProps {
  posts: ContentlayerBlogPost[];
  title?: string;
}

export default function RelatedPosts({
  posts,
  title = "Related Articles",
}: RelatedPostsProps) {
  if (posts.length === 0) {
    return null;
  }

  return (
    <section className="space-y-6">
      {/* Section Header */}
      <div className="flex items-center gap-2">
        <BookOpen className="h-6 w-6 text-primary" />
        <h2 className="text-2xl font-bold text-primary">{title}</h2>
      </div>

      {/* Posts Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {posts.map((post) => (
          <RelatedPostCard key={post.slug} post={post} />
        ))}
      </div>

      {/* View More Link */}
      <div className="pt-4 text-center">
        <Link
          href="/blog"
          className="inline-flex items-center gap-2 font-medium text-primary hover:underline"
        >
          View All Articles
          <ArrowRight className="h-4 w-4" />
        </Link>
      </div>
    </section>
  );
}

interface RelatedPostCardProps {
  post: ContentlayerBlogPost;
}

function RelatedPostCard({ post }: RelatedPostCardProps) {
  return (
    <Card className="group overflow-hidden border-border/50 transition-all duration-300 hover:border-primary/20 hover:shadow-lg">
      <div className="relative">
        {/* Post Image */}
        {post.image ? (
          <div className="relative h-40 overflow-hidden">
            <Image
              src={post.image.src}
              alt={post.image.alt}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, 33vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

            {/* Category Badge */}
            <Badge
              variant="secondary"
              className="absolute left-3 top-3 bg-background/90 text-xs text-foreground"
            >
              {post.categorySlug.replace("-", " ").toUpperCase()}
            </Badge>

            {/* Featured Badge */}
            {post.featured && (
              <Badge
                variant="default"
                className="absolute right-3 top-3 bg-primary text-xs text-primary-foreground"
              >
                Featured
              </Badge>
            )}
          </div>
        ) : (
          // Fallback for posts without images
          <div className="relative flex h-40 items-center justify-center bg-gradient-to-br from-primary/10 to-accent/10">
            <div className="space-y-2 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/20">
                <span className="text-lg font-bold text-primary">
                  {post.title.charAt(0)}
                </span>
              </div>
              <Badge variant="outline" className="text-xs">
                {post.categorySlug.replace("-", " ").toUpperCase()}
              </Badge>
            </div>
          </div>
        )}
      </div>

      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Title */}
          <h3 className="line-clamp-2 text-lg font-bold leading-tight transition-colors group-hover:text-primary">
            <Link href={post.url}>{post.title}</Link>
          </h3>

          {/* Excerpt */}
          <p className="line-clamp-2 text-sm text-muted-foreground">
            {post.excerpt}
          </p>

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {post.tags.slice(0, 2).map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="px-2 py-0.5 text-xs"
                >
                  {tag}
                </Badge>
              ))}
              {post.tags.length > 2 && (
                <Badge variant="outline" className="px-2 py-0.5 text-xs">
                  +{post.tags.length - 2}
                </Badge>
              )}
            </div>
          )}

          {/* Meta Information */}
          <div className="flex items-center gap-3 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatPublishDate(post.publishedAt)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{formatReadingTime(post.readingTime)}</span>
            </div>
          </div>

          {/* Read More Link */}
          <div className="pt-2">
            <Link
              href={post.url}
              className="inline-flex items-center gap-1 text-sm font-medium text-primary transition-all hover:underline group-hover:gap-2"
            >
              Read Article
              <ArrowRight className="h-3 w-3" />
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Compact version for sidebar
export function RelatedPostsCompact({
  posts,
  title = "You might also like",
}: RelatedPostsProps) {
  if (posts.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <h4 className="font-semibold text-foreground">{title}</h4>

      <div className="space-y-3">
        {posts.slice(0, 3).map((post) => (
          <div key={post.slug} className="group flex gap-3">
            {/* Post Image */}
            {post.image ? (
              <div className="relative h-12 w-16 flex-shrink-0 overflow-hidden rounded">
                <Image
                  src={post.image.src}
                  alt={post.image.alt}
                  fill
                  className="object-cover transition-transform group-hover:scale-105"
                  sizes="64px"
                />
              </div>
            ) : (
              <div className="flex h-12 w-16 flex-shrink-0 items-center justify-center rounded bg-primary/10">
                <span className="text-xs font-bold text-primary">
                  {post.title.charAt(0)}
                </span>
              </div>
            )}

            {/* Post Content */}
            <div className="min-w-0 flex-1">
              <h5 className="line-clamp-2 text-sm font-medium leading-tight transition-colors group-hover:text-primary">
                <Link href={post.url}>{post.title}</Link>
              </h5>
              <div className="mt-1 flex items-center gap-2 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>{formatPublishDate(post.publishedAt)}</span>
                <Clock className="h-3 w-3" />
                <span>{formatReadingTime(post.readingTime)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {posts.length > 3 && (
        <Link
          href="/blog"
          className="inline-flex items-center gap-1 text-sm font-medium text-primary hover:underline"
        >
          View More Articles
          <ArrowRight className="h-3 w-3" />
        </Link>
      )}
    </div>
  );
}
